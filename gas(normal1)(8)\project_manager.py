import tkinter as tk
from tkinter import messagebox, filedialog
import json
import os
from datetime import datetime
import traceback

class ProjectManager:
    """项目管理器 - 负责项目的创建、保存、加载和导出"""
    
    def __init__(self, parent):
        self.parent = parent
        self.current_project = "未命名项目"
        
    def new_project(self):
        """创建新项目"""
        # 确认是否保存当前项目
        if self.parent.current_project and messagebox.askyesno("确认", "是否保存当前项目？"):
            self.save_project()
            
        # 清空所有输入字段
        self.clear_all_fields()
        
        # 重置项目名称
        self.parent.current_project = "未命名项目"
        self.parent.project_name.set("")
        self.parent.project_code.set("")
        
        # 更新UI
        self.parent.update()
        
        messagebox.showinfo("成功", "已创建新项目")
        
    def clear_all_fields(self):
        """清空所有输入字段"""
        # 清空项目信息
        self.parent.project_name.set("")
        self.parent.project_code.set("")
        self.parent.project_type.set("")
        self.parent.daily_capacity.set("")
        self.parent.furnace_count.set("")
        self.parent.line_count.set("")
        
        # 清空流量信息
        self.parent.air_continuous_total.set("")
        self.parent.air_intermittent_total.set("")
        self.parent.air_total_flow.set("")
        
        # 清空压力信息
        self.parent.air_pressure.set("")
        
        # 清空管径信息
        self.parent.air_calculated_diameter.set("")
        self.parent.air_selected_diameter.set("")
        
        # 清空流速和温度信息
        self.parent.air_velocity.set("")
        self.parent.air_temperature.set("")
        
        # 清空小炉信息
        for furnace in self.parent.furnaces:
            furnace['heat_load'].set("")
            furnace['float_value'].set("")
            furnace['nozzle_count'].set("")
            
        # 清空阀门信息
        self.parent.main_valve_c_selected_value.set("")
        self.parent.main_valve_k_max_value.set("")
        self.parent.main_valve_k_min_value.set("")
        self.parent.release_valve_dn_value.set("")
        self.parent.release_valve_area_value.set("")
        self.parent.release_valve_d0_value.set("")
        
        # 重置氧枪和全氧窑选项
        self.parent.has_oxygen_lance.set("否")
        self.parent.is_oxygen_kiln.set("否")
        
        # 更新氧气工具按钮状态
        self.parent.update_oxygen_tools_state()
        
    def save_project(self, show_message=True):
        """保存当前项目"""
        try:
            # 收集项目数据
            project_data = {
                "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "工程名称": self.parent.project_name.get(),
                "工程代号": self.parent.project_code.get(),
                "项目类型": self.parent.project_type.get(),
                "吨位": self.parent.daily_capacity.get(),
                "小炉数": self.parent.furnace_count.get(),
                "一窑几线": self.parent.line_count.get(),
                "是否有0#氧枪": self.parent.has_oxygen_lance.get(),
                "是否是全氧窑": self.parent.is_oxygen_kiln.get(),
                
                # 用气需求数据
                "进车间压力(MPa)": self.parent.air_pressure.get(),
                "设计流速(m/s)": self.parent.air_velocity.get(),
                "连续用气总量(Nm³/h)": self.parent.air_continuous_total.get(),
                "间歇用气总量(Nm³/h)": self.parent.air_intermittent_total.get(),
                "总用气量(Nm³/h)": self.parent.air_total_flow.get(),
                "计算管径(mm)": self.parent.air_calculated_diameter.get(),
                "选取管径(mm)": self.parent.air_selected_diameter.get(),
                "实际流速(m/s)": self.parent.air_actual_velocity.get(),
                "温度(℃)": self.parent.air_temperature.get(),
            }
            
            # 添加小炉数据
            for i, furnace in enumerate(self.parent.furnaces):
                project_data[f"小炉{i+1}平均热负荷"] = furnace['heat_load'].get()
                project_data[f"小炉{i+1}浮动值"] = furnace['float_value'].get()
                project_data[f"小炉{i+1}喷枪数"] = furnace['nozzle_count'].get()
                project_data[f"小炉{i+1}调节阀C选定"] = furnace.get('c_selected', "")
                
            # 添加阀门数据
            project_data["主管调节阀C选定"] = self.parent.main_valve_c_selected_value.get()
            project_data["主管调节阀K大"] = self.parent.main_valve_k_max_value.get()
            project_data["主管调节阀K小"] = self.parent.main_valve_k_min_value.get()
            project_data["放散阀选取DN"] = self.parent.release_valve_dn_value.get()
            project_data["放散阀面积"] = self.parent.release_valve_area_value.get()
            project_data["放散阀d0"] = self.parent.release_valve_d0_value.get()
            
            # 添加设备表数据
            if hasattr(self.parent, 'equipment_manager'):
                project_data["equipment_data"] = self.parent.equipment_manager.get_equipment_data()
            
            # 加载现有历史记录
            history = []
            if os.path.exists(self.parent.history_file):
                with open(self.parent.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            
            # 检查是否已存在相同项目
            for i, record in enumerate(history):
                if (record.get("工程名称") == project_data["工程名称"] and 
                    record.get("工程代号") == project_data["工程代号"]):
                    # 更新现有记录
                    history[i] = project_data
                    break
            else:
                # 添加新记录
                history.append(project_data)
            
            # 写入历史记录文件
            with open(self.parent.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
            
            # 更新当前项目名称
            self.parent.current_project = self.parent.project_name.get() or "未命名项目"
            
            if show_message:
                messagebox.showinfo("成功", f"项目 '{project_data['工程名称']}' 已保存")
            
            print(f"项目 '{project_data['工程名称']}' 已保存")
            print(f"保存的调节阀数据: C选定={project_data.get('主管调节阀C选定')}, 放散阀DN={project_data.get('放散阀选取DN')}")
            
            return True
            
        except Exception as e:
            print(f"保存项目时出错: {str(e)}")
            traceback.print_exc()
            if show_message:
                messagebox.showerror("错误", f"保存项目时出错: {str(e)}")
            return False
    
    def load_project(self, project_data):
        """加载项目数据"""
        try:
            # 填充项目信息
            self.parent.project_name.set(project_data.get("工程名称", ""))
            self.parent.project_code.set(project_data.get("工程代号", ""))
            self.parent.project_type.set(project_data.get("项目类型", ""))
            self.parent.daily_capacity.set(project_data.get("吨位", ""))
            self.parent.furnace_count.set(project_data.get("小炉数", ""))
            self.parent.line_count.set(project_data.get("一窑几线", ""))
            
            # 填充氧枪和全氧窑选项
            self.parent.has_oxygen_lance.set(project_data.get("是否有0#氧枪", "否"))
            self.parent.is_oxygen_kiln.set(project_data.get("是否是全氧窑", "否"))
            
            # 填充用气需求数据
            self.parent.air_pressure.set(project_data.get("进车间压力(MPa)", ""))
            self.parent.air_velocity.set(project_data.get("设计流速(m/s)", ""))
            self.parent.air_continuous_total.set(project_data.get("连续用气总量(Nm³/h)", ""))
            self.parent.air_intermittent_total.set(project_data.get("间歇用气总量(Nm³/h)", ""))
            self.parent.air_total_flow.set(project_data.get("总用气量(Nm³/h)", ""))
            self.parent.air_calculated_diameter.set(project_data.get("计算管径(mm)", ""))
            self.parent.air_selected_diameter.set(project_data.get("选取管径(mm)", ""))
            
            # 填充小炉数据
            furnace_count = int(self.parent.furnace_count.get() or 1)
            for i in range(furnace_count):
                if i < len(self.parent.furnaces):
                    self.parent.furnaces[i]['heat_load'].set(project_data.get(f"小炉{i+1}平均热负荷", ""))
                    self.parent.furnaces[i]['float_value'].set(project_data.get(f"小炉{i+1}浮动值", ""))
                    self.parent.furnaces[i]['nozzle_count'].set(project_data.get(f"小炉{i+1}喷枪数", ""))
                    
                    # 保存小炉调节阀C选定值
                    c_selected = project_data.get(f"小炉{i+1}调节阀C选定", "")
                    if c_selected:
                        self.parent.furnaces[i]['c_selected'] = c_selected
            
            # 填充阀门数据
            self.parent.main_valve_c_selected_value.set(project_data.get("主管调节阀C选定", ""))
            self.parent.main_valve_k_max_value.set(project_data.get("主管调节阀K大", ""))
            self.parent.main_valve_k_min_value.set(project_data.get("主管调节阀K小", ""))
            self.parent.release_valve_dn_value.set(project_data.get("放散阀选取DN", ""))
            self.parent.release_valve_area_value.set(project_data.get("放散阀面积", ""))
            self.parent.release_valve_d0_value.set(project_data.get("放散阀d0", ""))
            
            # 加载设备表数据
            if "equipment_data" in project_data and hasattr(self.parent, 'equipment_manager'):
                self.parent.equipment_manager.load_equipment_data(project_data["equipment_data"])
            
            # 如果存在氧枪数据且氧枪开关是"是"，则主动加载氧枪数据
            if self.parent.has_oxygen_lance.get() == "是":
                try:
                    # 确保传递项目数据给氧枪计算器
                    self.parent.oxygen_lance_calculator.load_data()
                    print("已加载0#氧枪计算器数据")
                except Exception as e:
                    print(f"加载氧枪计算器数据时出错: {str(e)}")
                    traceback.print_exc()
            
            # 如果存在压缩空气相关数据，则加载压缩空气计算器
            if any(key.startswith("压缩空气") or key in ["进车间压力", "设计流速", "连续用气总量"] for key in project_data):
                print("发现压缩空气相关数据，准备加载...")
                # 确保compressed_air_calculator已初始化
                if not hasattr(self.parent, 'compressed_air_calculator') or self.parent.compressed_air_calculator is None:
                    from compressed_air import CompressedAirCalculator
                    self.parent.compressed_air_calculator = CompressedAirCalculator(self.parent)
                    print("重新初始化压缩空气计算器")
            
            # 触发计算
            self.parent.refresh_all_calculations()
            
            # 更新当前项目名称
            self.parent.current_project = self.parent.project_name.get() or "未命名项目"
            
            # 根据氧枪和全氧窑选项更新按钮状态
            self.parent.update_oxygen_tools_state()
            
            return True
            
        except Exception as e:
            print(f"加载项目数据时出错: {str(e)}")
            traceback.print_exc()
            messagebox.showerror("错误", f"加载项目数据时出错: {str(e)}")
            return False
    
    def export_to_word(self):
        """导出项目到Word文档"""
        try:
            # 先保存项目
            if not self.save_project(show_message=False):
                return
                
            # 打开文件选择对话框
            file_path = filedialog.asksaveasfilename(
                defaultextension=".docx",
                filetypes=[("Word文档", "*.docx"), ("所有文件", "*.*")],
                title="导出Word文档",
                initialdir=os.path.expanduser("~"),
                initialfile=f"{self.parent.project_name.get() or '未命名项目'}.docx"
            )
            
            if not file_path:
                return
                
            # 创建Word文档
            import docx
            doc = docx.Document()
            
            # 添加标题